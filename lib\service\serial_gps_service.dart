import 'dart:async';
import 'dart:convert';
import 'dart:io';

/// 高精度GPS串口服务
/// 用于读取/dev/ttyUSB3串口的GPS模块数据
class SerialGPSService {
  static SerialGPSService? _instance;
  static SerialGPSService get instance => _instance ??= SerialGPSService._();
  
  SerialGPSService._();

  // 串口配置
  static const String _serialPort = '/dev/ttyS7';
  static const int _baudRate = 115200;
  
  // 串口相关
  Process? _serialProcess;
  Process? _writeProcess;
  StreamSubscription? _dataSubscription;
  bool _isConnected = false;

  // 低功耗模式定时器
  Timer? _lowPowerTimer;
  static const Duration _lowPowerInterval = Duration(seconds: 15);
  
  // GPS数据
  double? _latitude;
  double? _longitude;
  double? _altitude;
  String? _fixQuality;
  int? _satelliteCount;
  DateTime? _lastUpdateTime;
  
  // 数据监听器
  final List<Function(GPSData)> _listeners = [];
  
  /// 当前连接状态
  bool get isConnected => _isConnected;

  /// 发送NMEA指令到GPS模块
  Future<bool> sendNMEACommand(String command) async {
    if (!_isConnected) {
      print('GPS模块未连接，无法发送指令');
      return false;
    }

    try {
      // 确保指令以$开头并包含正确的格式
      String nmeaCommand = command;
      if (!nmeaCommand.startsWith('\$')) {
        nmeaCommand = '\$' + nmeaCommand;
      }

      // 添加回车换行符
      if (!nmeaCommand.endsWith('\r\n')) {
        nmeaCommand += '\r\n';
      }

      print('发送NMEA指令: ${nmeaCommand.trim()}');

      // 使用echo命令将指令写入串口
      final writeProcess = await Process.start(
        'sh',
        ['-c', 'echo -ne "${nmeaCommand.replaceAll('\$', '\\\$').replaceAll('\r', '\\\r').replaceAll('\n', '\\\n')}" > $_serialPort'],
        mode: ProcessStartMode.normal,
      );

      final exitCode = await writeProcess.exitCode;
      if (exitCode == 0) {
        print('✅ NMEA指令发送成功');
        return true;
      } else {
        print('❌ NMEA指令发送失败，退出码: $exitCode');
        return false;
      }
    } catch (e) {
      print('❌ 发送NMEA指令时出错: $e');
      return false;
    }
  }

  /// 初始化GPS模块（启动低功耗定时器）
  Future<bool> initializeGPSModule() async {
    if (!_isConnected) {
      print('GPS模块未连接，无法初始化');
      return false;
    }

    try {
      print('🔧 正在初始化GPS模块...');

      // 立即发送第一次低功耗指令
      final success = await _sendLowPowerCommand();

      if (success) {
        // 启动定时器，每15秒发送一次低功耗指令
        _startLowPowerTimer();
        print('✅ GPS模块低功耗定时器已启动（每15秒发送一次指令）');
        return true;
      } else {
        print('❌ GPS模块初始化失败');
        return false;
      }
    } catch (e) {
      print('❌ GPS模块初始化时出错: $e');
      return false;
    }
  }

  /// 发送低功耗指令
  Future<bool> _sendLowPowerCommand() async {
    try {
      print('📡 发送GPS低功耗指令...');
      // 发送低功耗指令：$PAIR650,10*14
      // 这个指令将GPS模块设置为10秒更新间隔的低功耗模式
      final success = await sendNMEACommand('\$PAIR650,10*14');

      if (success) {
        print('✅ 低功耗指令发送成功');
      } else {
        print('❌ 低功耗指令发送失败');
      }

      return success;
    } catch (e) {
      print('❌ 发送低功耗指令时出错: $e');
      return false;
    }
  }

  /// 启动低功耗定时器
  void _startLowPowerTimer() {
    // 先停止现有定时器（如果存在）
    _stopLowPowerTimer();

    // 创建新的定时器，每15秒执行一次
    _lowPowerTimer = Timer.periodic(_lowPowerInterval, (timer) async {
      if (_isConnected) {
        await _sendLowPowerCommand();
      } else {
        // 如果连接断开，停止定时器
        _stopLowPowerTimer();
      }
    });

    print('⏰ 低功耗定时器已启动，间隔: ${_lowPowerInterval.inSeconds}秒');
  }

  /// 停止低功耗定时器
  void _stopLowPowerTimer() {
    if (_lowPowerTimer != null) {
      _lowPowerTimer!.cancel();
      _lowPowerTimer = null;
      print('⏰ 低功耗定时器已停止');
    }
  }
  
  /// 最新GPS数据
  GPSData? get latestData => _latitude != null && _longitude != null
      ? GPSData(
          latitude: _latitude!,
          longitude: _longitude!,
          altitude: _altitude,
          fixQuality: _fixQuality,
          satelliteCount: _satelliteCount,
          timestamp: _lastUpdateTime ?? DateTime.now(),
        )
      : null;

  /// 添加数据监听器
  void addListener(Function(GPSData) listener) {
    _listeners.add(listener);
  }

  /// 移除数据监听器
  void removeListener(Function(GPSData) listener) {
    _listeners.remove(listener);
  }

  /// 连接串口
  Future<bool> connect() async {
    try {
      //adb shell settings put global policy_control immersive.full=com.example.a325_lya_1000正在连接 $_serialPort (波特率: $_baudRate)');

      // 检查串口设备是否存在
      final serialFile = File(_serialPort);
      if (!await serialFile.exists()) {
        //adb shell settings put global policy_control immersive.full=com.example.a325_lya_1000错误: 设备 $_serialPort 不存在');
        return false;
      }

      // 尝试连接串口设备
      bool connected = false;

      // 方法1: 使用stty配置串口参数
      try {
        //adb shell settings put global policy_control immersive.full=com.example.a325_lya_1000尝试配置串口参数...');
        _serialProcess = await Process.start(
          'sh',
          ['-c', 'stty -F $_serialPort $_baudRate cs8 -cstopb -parenb raw && cat $_serialPort'],
          mode: ProcessStartMode.normal,
        );
        connected = true;
        //adb shell settings put global policy_control immersive.full=com.example.a325_lya_1000✅ 串口连接成功 (配置模式)');
      } catch (e) {
        //adb shell settings put global policy_control immersive.full=com.example.a325_lya_1000配置模式失败: $e');
      }

      // 方法2: 直接读取（如果配置失败）
      if (!connected) {
        try {
          //adb shell settings put global policy_control immersive.full=com.example.a325_lya_1000尝试直接读取模式...');
          _serialProcess = await Process.start('cat', [_serialPort]);
          connected = true;
          //adb shell settings put global policy_control immersive.full=com.example.a325_lya_1000✅ 串口连接成功 (直接模式)');
        } catch (e) {
          //adb shell settings put global policy_control immersive.full=com.example.a325_lya_1000直接模式失败: $e');
        }
      }

      if (!connected || _serialProcess == null) {
        //adb shell settings put global policy_control immersive.full=com.example.a325_lya_1000❌ 连接失败');
        return false;
      }

      // 监听数据流
      _dataSubscription = _serialProcess!.stdout
          .transform(utf8.decoder)
          .transform(const LineSplitter())
          .listen(
            _processNMEAData,
            onError: (error) {
              //adb shell settings put global policy_control immersive.full=com.example.a325_lya_1000数据读取错误: $error');
              _handleDisconnection();
            },
            onDone: () {
              //adb shell settings put global policy_control immersive.full=com.example.a325_lya_1000数据流结束');
              _handleDisconnection();
            },
          );

      _isConnected = true;
      //adb shell settings put global policy_control immersive.full=com.example.a325_lya_1000连接成功');

      // 连接成功后初始化GPS模块（发送低功耗指令）
      await Future.delayed(Duration(milliseconds: 1000)); // 等待连接稳定
      await initializeGPSModule();

      return true;
      
    } catch (e) {
      //adb shell settings put global policy_control immersive.full=com.example.a325_lya_1000连接失败: $e');
      return false;
    }
  }

  /// 断开串口连接
  Future<void> disconnect() async {
    try {
      //adb shell settings put global policy_control immersive.full=com.example.a325_lya_1000断开连接');

      // 停止低功耗定时器
      _stopLowPowerTimer();

      await _dataSubscription?.cancel();
      _dataSubscription = null;

      _serialProcess?.kill();
      await _serialProcess?.exitCode;
      _serialProcess = null;

      _writeProcess?.kill();
      await _writeProcess?.exitCode;
      _writeProcess = null;

      _isConnected = false;
      _clearGPSData();

      //adb shell settings put global policy_control immersive.full=com.example.a325_lya_1000连接已断开');
    } catch (e) {
      //adb shell settings put global policy_control immersive.full=com.example.a325_lya_1000断开连接时出错: $e');
    }
  }

  /// 处理连接断开
  void _handleDisconnection() {
    _isConnected = false;
    _clearGPSData();
  }

  /// 清除GPS数据
  void _clearGPSData() {
    _latitude = null;
    _longitude = null;
    _altitude = null;
    _fixQuality = null;
    _satelliteCount = null;
    _lastUpdateTime = null;
  }

  /// 处理NMEA数据
  void _processNMEAData(String line) {
    try {
      line = line.trim();
      if (line.isEmpty) return;

      // 只打印有效的NMEA语句
      if (line.startsWith('\$')) {
        //adb shell settings put global policy_control immersive.full=com.example.a325_lya_1000📡 $line');
      }

      // 验证NMEA校验和
      if (!_validateNMEAChecksum(line)) {
        return; // 静默忽略校验和错误
      }

      // 解析不同类型的NMEA语句
      if (line.startsWith('\$GPGGA') || line.startsWith('\$GNGGA')) {
        _parseGGA(line);
      } else if (line.startsWith('\$GPRMC') || line.startsWith('\$GNRMC')) {
        _parseRMC(line);
      } else if (line.startsWith('\$GPGSV') || line.startsWith('\$GNGSV')) {
        _parseGSV(line);
      }

    } catch (e) {
      //adb shell settings put global policy_control immersive.full=com.example.a325_lya_1000⚠️ 解析错误: $e');
    }
  }

  /// 验证NMEA校验和
  bool _validateNMEAChecksum(String sentence) {
    if (!sentence.contains('*')) return false;
    
    try {
      final parts = sentence.split('*');
      if (parts.length != 2) return false;
      
      final data = parts[0].substring(1); // 去掉开头的$
      final checksumStr = parts[1];
      
      int calculatedChecksum = 0;
      for (int i = 0; i < data.length; i++) {
        calculatedChecksum ^= data.codeUnitAt(i);
      }
      
      final expectedChecksum = int.parse(checksumStr, radix: 16);
      return calculatedChecksum == expectedChecksum;
    } catch (e) {
      return false;
    }
  }

  /// 解析GGA语句（全球定位系统固定数据）
  void _parseGGA(String sentence) {
    try {
      final parts = sentence.split(',');
      if (parts.length < 15) return;
      
      // 解析纬度
      if (parts[2].isNotEmpty && parts[3].isNotEmpty) {
        _latitude = _parseCoordinate(parts[2], parts[3]);
      }
      
      // 解析经度
      if (parts[4].isNotEmpty && parts[5].isNotEmpty) {
        _longitude = _parseCoordinate(parts[4], parts[5]);
      }
      
      // 解析高度
      if (parts[9].isNotEmpty) {
        _altitude = double.tryParse(parts[9]);
      }
      
      // 解析定位质量
      if (parts[6].isNotEmpty) {
        final quality = int.tryParse(parts[6]) ?? 0;
        _fixQuality = _getFixQualityDescription(quality);
      }
      
      // 解析卫星数量
      if (parts[7].isNotEmpty) {
        _satelliteCount = int.tryParse(parts[7]);
      }
      
      _lastUpdateTime = DateTime.now();
      _notifyListeners();

      //adb shell settings put global policy_control immersive.full=com.example.a325_lya_1000📍 位置更新 - 纬度: ${_latitude?.toStringAsFixed(6)}, 经度: ${_longitude?.toStringAsFixed(6)}, 高度: ${_altitude?.toStringAsFixed(1)}m');

    } catch (e) {
      // 静默处理解析错误
    }
  }

  /// 解析RMC语句（推荐最小定位信息）
  void _parseRMC(String sentence) {
    try {
      final parts = sentence.split(',');
      if (parts.length < 12) return;
      
      // 检查数据有效性
      if (parts[2] != 'A') return; // A表示有效，V表示无效
      
      // 解析纬度
      if (parts[3].isNotEmpty && parts[4].isNotEmpty) {
        _latitude = _parseCoordinate(parts[3], parts[4]);
      }
      
      // 解析经度
      if (parts[5].isNotEmpty && parts[6].isNotEmpty) {
        _longitude = _parseCoordinate(parts[5], parts[6]);
      }
      
      _lastUpdateTime = DateTime.now();
      _notifyListeners();

    } catch (e) {
      // 静默处理解析错误
    }
  }

  /// 解析GSV语句（可见卫星信息）
  void _parseGSV(String sentence) {
    try {
      final parts = sentence.split(',');
      if (parts.length < 4) return;
      
      // 解析可见卫星总数
      if (parts[3].isNotEmpty) {
        _satelliteCount = int.tryParse(parts[3]);
      }
      
    } catch (e) {
      // 静默处理解析错误
    }
  }

  /// 解析坐标（度分格式转十进制度）
  double? _parseCoordinate(String coordinate, String direction) {
    try {
      if (coordinate.isEmpty || direction.isEmpty) return null;
      
      // 纬度格式: ddmm.mmmm, 经度格式: dddmm.mmmm
      final isLatitude = coordinate.length <= 9;
      final degreeLength = isLatitude ? 2 : 3;
      
      final degrees = double.parse(coordinate.substring(0, degreeLength));
      final minutes = double.parse(coordinate.substring(degreeLength));
      
      double result = degrees + (minutes / 60.0);
      
      // 根据方向调整符号
      if (direction == 'S' || direction == 'W') {
        result = -result;
      }
      
      return result;
    } catch (e) {
      return null;
    }
  }

  /// 获取定位质量描述
  String _getFixQualityDescription(int quality) {
    switch (quality) {
      case 0: return '无效';
      case 1: return 'GPS定位';
      case 2: return 'DGPS定位';
      case 3: return 'PPS定位';
      case 4: return 'RTK固定解';
      case 5: return 'RTK浮点解';
      case 6: return '估算';
      case 7: return '手动输入';
      case 8: return '模拟模式';
      default: return '未知';
    }
  }

  /// 通知监听器
  void _notifyListeners() {
    final data = latestData;
    if (data != null) {
      for (final listener in _listeners) {
        try {
          listener(data);
        } catch (e) {
          // 静默处理监听器错误
        }
      }
    }
  }

  /// 释放资源
  Future<void> dispose() async {
    await disconnect();
    _listeners.clear();
  }
}

/// GPS数据模型
class GPSData {
  final double latitude;
  final double longitude;
  final double? altitude;
  final String? fixQuality;
  final int? satelliteCount;
  final DateTime timestamp;

  GPSData({
    required this.latitude,
    required this.longitude,
    this.altitude,
    this.fixQuality,
    this.satelliteCount,
    required this.timestamp,
  });

  @override
  String toString() {
    return 'GPSData{纬度: $latitude, 经度: $longitude, 高度: $altitude, '
           '定位质量: $fixQuality, 卫星数: $satelliteCount, 时间: $timestamp}';
  }
}
