import 'lib/service/serial_gps_service.dart';

/// 测试修正后的GPS低功耗功能
void main() async {
  print('🚀 测试修正后的GPS低功耗功能');
  print('='*50);
  
  final gpsService = SerialGPSService.instance;
  
  try {
    // 1. 连接GPS服务
    print('🔌 正在连接GPS模块...');
    final connected = await gpsService.connect();
    
    if (!connected) {
      print('❌ GPS模块连接失败');
      return;
    }
    
    print('✅ GPS模块连接成功');
    
    // 2. 等待初始化完成
    print('⏱️ 等待GPS模块初始化完成...');
    await Future.delayed(Duration(seconds: 3));
    
    // 3. 手动测试一次指令发送
    print('\n📡 手动测试低功耗指令发送...');
    final testSuccess = await gpsService.sendNMEACommand('PAIR650,10*14');
    
    if (testSuccess) {
      print('✅ 手动测试成功');
    } else {
      print('❌ 手动测试失败');
    }
    
    // 4. 监听GPS数据变化
    print('\n📊 开始监听GPS数据（30秒）...');
    print('💡 观察数据更新间隔是否变为约10秒');
    
    int dataCount = 0;
    DateTime? lastDataTime;
    
    gpsService.addListener((gpsData) {
      dataCount++;
      final now = DateTime.now();
      
      String intervalInfo = '';
      if (lastDataTime != null) {
        final interval = now.difference(lastDataTime!).inSeconds;
        intervalInfo = ' (间隔: ${interval}秒)';
      }
      
      print('📍 GPS数据[$dataCount]$intervalInfo - 纬度: ${gpsData.latitude.toStringAsFixed(6)}, 经度: ${gpsData.longitude.toStringAsFixed(6)}');
      lastDataTime = now;
    });
    
    // 监听30秒
    await Future.delayed(Duration(seconds: 30));
    
    // 5. 分析结果
    print('\n📈 数据分析:');
    print('总数据更新次数: $dataCount');
    
    if (dataCount > 0) {
      final avgInterval = 30.0 / dataCount;
      print('平均更新间隔: ${avgInterval.toStringAsFixed(1)}秒');
      
      if (avgInterval >= 8 && avgInterval <= 12) {
        print('✅ GPS模块已成功进入低功耗模式');
      } else if (avgInterval < 5) {
        print('⚠️ GPS模块可能未进入低功耗模式（更新频率过高）');
      } else {
        print('ℹ️ GPS模块更新间隔异常');
      }
    } else {
      print('⚠️ 未收到GPS数据更新');
    }
    
    // 6. 观察定时器工作
    print('\n⏰ 继续观察定时器工作（30秒）...');
    print('💡 应该每15秒看到一次低功耗指令发送日志');
    
    await Future.delayed(Duration(seconds: 30));
    
  } catch (e) {
    print('❌ 测试过程中发生错误: $e');
  } finally {
    // 清理资源
    print('\n🔌 断开GPS连接...');
    await gpsService.disconnect();
    print('✅ 测试完成');
  }
  
  print('\n' + '='*50);
  print('📋 测试结果说明:');
  print('1. 如果看到"GPS模块已成功进入低功耗模式"，说明指令有效');
  print('2. 如果看到定时器相关日志，说明定时器正常工作');
  print('3. 如果GPS数据更新间隔约为10秒，说明低功耗模式生效');
  print('4. 如果看到"临时文件方法"日志，说明使用了备用发送方式');
  print('='*50);
}
