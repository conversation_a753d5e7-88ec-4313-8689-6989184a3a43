import 'lib/service/serial_gps_service.dart';

/// 最终GPS低功耗指令测试
void main() async {
  print('🚀 最终GPS低功耗指令测试');
  print('='*60);
  
  final gpsService = SerialGPSService.instance;
  
  try {
    // 1. 连接GPS服务
    print('🔌 正在连接GPS模块...');
    final connected = await gpsService.connect();
    
    if (!connected) {
      print('❌ GPS模块连接失败');
      return;
    }
    
    print('✅ GPS模块连接成功');
    
    // 2. 等待连接稳定
    print('⏱️ 等待连接稳定...');
    await Future.delayed(Duration(seconds: 2));
    
    // 3. 手动发送一次低功耗指令进行测试
    print('\n📡 手动发送低功耗指令测试...');
    final manualSuccess = await gpsService.sendNMEACommand('PAIR650,10*14');
    
    if (manualSuccess) {
      print('✅ 手动指令发送成功');
    } else {
      print('❌ 手动指令发送失败');
    }
    
    // 4. 等待观察GPS模块响应
    print('\n⏱️ 等待10秒观察GPS模块响应...');
    print('💡 请观察GPS模块是否进入低功耗模式（数据更新间隔变为10秒）');
    
    // 添加GPS数据监听器
    int dataCount = 0;
    DateTime? lastDataTime;
    
    gpsService.addListener((gpsData) {
      dataCount++;
      final now = DateTime.now();
      
      if (lastDataTime != null) {
        final interval = now.difference(lastDataTime!).inSeconds;
        print('📍 GPS数据[$dataCount] - 间隔: ${interval}秒 - 纬度: ${gpsData.latitude.toStringAsFixed(6)}, 经度: ${gpsData.longitude.toStringAsFixed(6)}');
      } else {
        print('📍 GPS数据[$dataCount] - 纬度: ${gpsData.latitude.toStringAsFixed(6)}, 经度: ${gpsData.longitude.toStringAsFixed(6)}');
      }
      
      lastDataTime = now;
    });
    
    // 等待观察
    await Future.delayed(Duration(seconds: 30));
    
    // 5. 测试定时器功能
    print('\n⏰ 定时器已自动启动，将每15秒发送一次低功耗指令');
    print('📊 继续监听30秒，观察定时器工作情况...');
    
    await Future.delayed(Duration(seconds: 30));
    
    print('\n📈 总共收到 $dataCount 次GPS数据更新');
    if (dataCount > 0) {
      final avgInterval = 60.0 / dataCount;
      print('📊 平均更新间隔: ${avgInterval.toStringAsFixed(1)}秒');
      
      if (avgInterval >= 8 && avgInterval <= 12) {
        print('✅ GPS模块已成功进入低功耗模式（约10秒间隔）');
      } else if (avgInterval < 5) {
        print('⚠️ GPS模块可能未进入低功耗模式（更新频率过高）');
      } else {
        print('ℹ️ GPS模块更新间隔: ${avgInterval.toStringAsFixed(1)}秒');
      }
    }
    
  } catch (e) {
    print('❌ 测试过程中发生错误: $e');
  } finally {
    // 清理资源
    print('\n🔌 断开GPS连接...');
    await gpsService.disconnect();
    print('✅ 测试完成');
  }
  
  print('='*60);
  print('📋 测试总结:');
  print('1. 如果看到"GPS模块已成功进入低功耗模式"，说明指令有效');
  print('2. 如果看到每15秒的定时器日志，说明定时器工作正常');
  print('3. 如果GPS数据更新间隔约为10秒，说明低功耗模式生效');
  print('='*60);
}
