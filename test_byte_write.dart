import 'dart:convert';
import 'dart:io';

/// 测试字节级别的NMEA指令写入
void main() async {
  print('🧪 测试字节级别NMEA指令写入');
  print('='*50);
  
  // 构建正确的NMEA指令字节序列
  // $PAIR650,10*14\r\n
  final commandBytes = [
    0x24, // $
    0x50, 0x41, 0x49, 0x52, // PAIR
    0x36, 0x35, 0x30, // 650
    0x2C, // ,
    0x31, 0x30, // 10
    0x2A, // *
    0x31, 0x34, // 14
    0x0D, // \r
    0x0A, // \n
  ];
  
  print('📤 指令字节序列:');
  print('   ${commandBytes.map((b) => '0x${b.toRadixString(16).padLeft(2, '0')}').join(' ')}');
  
  // 转换为字符串验证
  final commandString = String.fromCharCodes(commandBytes);
  print('🔤 对应字符串: "${commandString.replaceAll('\r', '\\r').replaceAll('\n', '\\n')}"');
  
  try {
    // 写入设备文件
    print('\n📝 写入 /dev/ttyS7...');
    final deviceFile = File('/dev/ttyS7');
    await deviceFile.writeAsBytes(commandBytes, mode: FileMode.writeOnlyAppend);
    
    print('✅ 字节写入成功！');
    print('⏱️ 请观察GPS模块是否进入低功耗模式');
    
  } catch (e) {
    print('❌ 字节写入失败: $e');
    print('💡 可能需要root权限或设备文件不存在');
  }
  
  print('\n🔍 对比分析:');
  print('Shell命令: echo -ne "\\$PAIR650,10*14\\r\\n" > /dev/ttyS7');
  print('字节序列: ${commandBytes.map((b) => '0x${b.toRadixString(16).padLeft(2, '0')}').join(' ')}');
  
  // 验证UTF-8编码是否一致
  final utf8Bytes = utf8.encode('\$PAIR650,10*14\r\n');
  print('UTF-8编码: ${utf8Bytes.map((b) => '0x${b.toRadixString(16).padLeft(2, '0')}').join(' ')}');
  
  if (commandBytes.length == utf8Bytes.length) {
    bool identical = true;
    for (int i = 0; i < commandBytes.length; i++) {
      if (commandBytes[i] != utf8Bytes[i]) {
        identical = false;
        print('❌ 字节[$i]不匹配: 手动=${commandBytes[i]} vs UTF8=${utf8Bytes[i]}');
      }
    }
    if (identical) {
      print('✅ 字节序列完全一致');
    }
  } else {
    print('❌ 字节长度不匹配: 手动=${commandBytes.length} vs UTF8=${utf8Bytes.length}');
  }
}
