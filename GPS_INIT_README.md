# GPS模块初始化和低功耗配置

## 概述

本项目已集成GPS模块自动初始化功能，在系统启动时会自动向GPS模块发送低功耗指令，确保模块进入节能工作状态。

## 功能特性

### 1. 自动初始化
- 系统启动时自动连接GPS模块（串口：/dev/ttyS7，波特率：115200）
- 连接成功后自动发送低功耗指令
- 无需手动干预，完全自动化

### 2. 低功耗指令和定时器
- **指令格式**: `$PAIR650,10*14<CR><LF>`
- **功能**: 将GPS模块设置为10秒更新间隔的低功耗模式
- **定时器**: 每15秒自动发送一次低功耗指令
- **效果**: 持续保持GPS模块在低功耗状态，显著降低功耗

### 3. NMEA指令支持
- 支持发送标准NMEA指令到GPS模块
- 自动添加必要的格式化字符（$前缀、回车换行符）
- 完整的错误处理和状态反馈

## 技术实现

### 核心方法

#### `sendNMEACommand(String command)`
```dart
// 发送NMEA指令到GPS模块
final success = await gpsService.sendNMEACommand('$PAIR650,10*14');
```

#### `initializeGPSModule()`
```dart
// 初始化GPS模块（自动调用）
await gpsService.initializeGPSModule();
```

### 自动执行流程

1. **连接串口** - 系统启动时自动连接/dev/ttyS7
2. **等待稳定** - 连接后等待1秒确保通信稳定
3. **发送首次指令** - 立即发送`$PAIR650,10*14`低功耗指令
4. **启动定时器** - 启动15秒间隔的定时器
5. **周期性发送** - 每15秒自动发送一次低功耗指令
6. **开始监听** - 开始接收GPS数据

## 指令说明

### PAIR650指令格式
```
$PAIR650,<Second>*<Checksum><CR><LF>
```

- **$PAIR650**: 指令标识符
- **<Second>**: 更新间隔（秒），本项目设置为10秒
- **<Checksum>**: 校验和，本项目为*14
- **<CR><LF>**: 回车换行符（\r\n）

### 定时器机制
- **间隔时间**: 15秒
- **工作原理**: 由于GPS模块的低功耗指令是一次性的，需要周期性发送以保持低功耗状态
- **自动管理**: 连接时自动启动，断开时自动停止
- **错误处理**: 如果连接断开，定时器会自动停止

### 其他支持的指令
- `$PAIR001*3D` - 查询GPS模块版本信息
- `$PAIR002*3E` - 查询当前配置信息

## 测试工具

### 运行测试
```bash
dart test_gps_init.dart
```

### 测试功能
1. **连接测试** - 验证串口连接是否正常
2. **初始化测试** - 验证低功耗指令是否发送成功
3. **定时器测试** - 验证15秒间隔的定时器是否正常工作
4. **数据监听** - 监听GPS数据更新
5. **手动指令** - 测试手动发送NMEA指令
6. **低功耗验证** - 验证10秒更新间隔是否生效

### 专门的定时器测试
```dart
// 运行2分钟的定时器测试
await GPSInitTest.testLowPowerTimer();
```

## 日志输出

系统会输出详细的日志信息：

```
🔧 正在初始化GPS模块...
📡 发送GPS低功耗指令...
发送NMEA指令: $PAIR650,10*14
✅ NMEA指令发送成功
✅ 低功耗指令发送成功
⏰ 低功耗定时器已启动，间隔: 15秒
✅ GPS模块低功耗定时器已启动（每15秒发送一次指令）

// 每15秒会看到：
📡 发送GPS低功耗指令...
发送NMEA指令: $PAIR650,10*14
✅ 低功耗指令发送成功
```

## 错误处理

- **连接失败**: 自动重试连接机制
- **指令发送失败**: 详细错误日志和状态码
- **格式错误**: 自动格式化NMEA指令
- **权限问题**: 清晰的错误提示和解决建议

## 注意事项

1. **串口权限**: 确保应用有访问/dev/ttyS7的权限
2. **GPS模块**: 确保GPS模块支持PAIR650指令
3. **更新间隔**: 10秒间隔适合大多数应用场景，如需调整可修改指令参数
4. **电源管理**: 低功耗模式可显著降低GPS模块功耗

## 文件结构

```
lib/
├── service/
│   └── serial_gps_service.dart    # GPS服务核心实现
├── utils/
│   └── gps_init_test.dart         # GPS初始化测试工具
└── test_gps_init.dart             # 测试主程序
```

## 版本历史

- **v1.0** - 基础GPS数据读取功能
- **v1.1** - 添加NMEA指令发送功能
- **v1.2** - 集成自动初始化和低功耗配置
- **v1.3** - 添加15秒间隔的低功耗定时器，确保GPS模块持续保持低功耗状态
