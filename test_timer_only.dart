import 'lib/utils/gps_init_test.dart';

/// 专门测试GPS低功耗定时器的程序
void main() async {
  print('🚀 启动GPS低功耗定时器测试程序');
  print('⏰ 此测试将运行2分钟，观察定时器工作情况');
  print('💡 预期每15秒会看到一次低功耗指令发送');
  print('='*60);
  
  try {
    // 执行定时器测试
    await GPSInitTest.testLowPowerTimer();
    
    print('\n' + '='*60);
    print('✅ 定时器测试完成！');
    print('📊 如果看到每15秒发送一次指令，说明定时器工作正常');
    print('='*60);
    
  } catch (e) {
    print('❌ 定时器测试失败: $e');
  }
}
