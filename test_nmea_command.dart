import 'dart:convert';
import 'dart:io';
import 'lib/service/serial_gps_service.dart';

/// 测试NMEA指令发送的程序
void main() async {
  print('🧪 测试NMEA指令发送功能');
  print('='*50);
  
  // 测试不同的指令发送方法
  await testDirectWrite();
  await testServiceMethod();
  
  print('='*50);
  print('✅ 测试完成');
}

/// 测试直接写入方法
Future<void> testDirectWrite() async {
  print('\n📝 测试1: 直接写入设备文件');
  
  try {
    final command = '\$PAIR650,10*14\r\n';
    final bytes = utf8.encode(command);
    
    print('🔤 指令字符串: ${command.replaceAll('\r', '\\r').replaceAll('\n', '\\n')}');
    print('📤 字节数组: ${bytes.map((b) => '0x${b.toRadixString(16).padLeft(2, '0')}').join(' ')}');
    
    // 直接写入设备文件
    final deviceFile = File('/dev/ttyS7');
    await deviceFile.writeAsBytes(bytes, mode: FileMode.writeOnlyAppend);
    
    print('✅ 直接写入成功');
    
  } catch (e) {
    print('❌ 直接写入失败: $e');
  }
}

/// 测试服务方法
Future<void> testServiceMethod() async {
  print('\n🔧 测试2: 使用GPS服务方法');
  
  try {
    final gpsService = SerialGPSService.instance;
    
    // 先连接GPS服务
    print('🔌 正在连接GPS服务...');
    final connected = await gpsService.connect();
    
    if (connected) {
      print('✅ GPS服务连接成功');
      
      // 等待一段时间让连接稳定
      await Future.delayed(Duration(seconds: 2));
      
      // 发送低功耗指令
      print('📡 发送低功耗指令...');
      final success = await gpsService.sendNMEACommand('\$PAIR650,10*14');
      
      if (success) {
        print('✅ 服务方法发送成功');
      } else {
        print('❌ 服务方法发送失败');
      }
      
      // 等待观察效果
      print('⏱️ 等待5秒观察GPS模块响应...');
      await Future.delayed(Duration(seconds: 5));
      
      // 断开连接
      await gpsService.disconnect();
      print('🔌 GPS服务已断开');
      
    } else {
      print('❌ GPS服务连接失败');
    }
    
  } catch (e) {
    print('❌ 服务方法测试失败: $e');
  }
}

/// 分析字节差异
void analyzeBytes() {
  print('\n🔍 分析字节差异:');
  
  // 正确的指令（在shell中有效）
  final correctCommand = '\$PAIR650,10*14\r\n';
  final correctBytes = utf8.encode(correctCommand);
  
  print('正确指令: ${correctCommand.replaceAll('\r', '\\r').replaceAll('\n', '\\n')}');
  print('正确字节: ${correctBytes.map((b) => '0x${b.toRadixString(16).padLeft(2, '0')}').join(' ')}');
  
  // 分析每个字符的ASCII值
  for (int i = 0; i < correctCommand.length; i++) {
    final char = correctCommand[i];
    final byte = correctBytes[i];
    final charDesc = char == '\r' ? '\\r' : char == '\n' ? '\\n' : char;
    print('字符[$i]: "$charDesc" = 0x${byte.toRadixString(16).padLeft(2, '0')} (${byte})');
  }
}
