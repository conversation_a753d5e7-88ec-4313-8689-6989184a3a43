import 'package:flutter/material.dart';
import '../utils/battery_manager.dart';
import '../theme/industrial_theme.dart';

/// 电池状态显示组件
class BatteryStatusWidget extends StatefulWidget {
  const BatteryStatusWidget({super.key});

  @override
  State<BatteryStatusWidget> createState() => _BatteryStatusWidgetState();
}

class _BatteryStatusWidgetState extends State<BatteryStatusWidget> {
  late BatteryManager _batteryManager;

  @override
  void initState() {
    super.initState();
    _batteryManager = BatteryManager.instance;
    _batteryManager.addListener(_onBatteryStatusChanged);

    // 启动电池监控，15秒更新一次
    if (!_batteryManager.isMonitoring) {
      _batteryManager.startMonitoring();
    }
  }

  @override
  void dispose() {
    _batteryManager.removeListener(_onBatteryStatusChanged);
    super.dispose();
  }

  void _onBatteryStatusChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final batteryInfo = _batteryManager.batteryInfo;
    
    if (batteryInfo == null) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.grey.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey, width: 1),
        ),
        child: const Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.battery_unknown, color: Colors.grey, size: 16),
            SizedBox(width: 4),
            Text(
              '--',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getBatteryBackgroundColor(batteryInfo),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getBatteryBorderColor(batteryInfo),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getBatteryIcon(batteryInfo),
            color: _getBatteryIconColor(batteryInfo),
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            '${batteryInfo.level}%',
            style: TextStyle(
              color: _getBatteryTextColor(batteryInfo),
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          if (batteryInfo.state == BatteryState.charging) ...[
            const SizedBox(width: 2),
            Icon(
              Icons.flash_on,
              color: IndustrialTheme.warningAmber,
              size: 12,
            ),
          ],
        ],
      ),
    );
  }

  /// 获取电池图标
  IconData _getBatteryIcon(BatteryInfo batteryInfo) {
    if (batteryInfo.state == BatteryState.charging) {
      return Icons.battery_charging_full;
    }
    
    if (batteryInfo.level >= 90) {
      return Icons.battery_full;
    } else if (batteryInfo.level >= 60) {
      return Icons.battery_5_bar;
    } else if (batteryInfo.level >= 40) {
      return Icons.battery_3_bar;
    } else if (batteryInfo.level >= 20) {
      return Icons.battery_2_bar;
    } else {
      return Icons.battery_1_bar;
    }
  }

  /// 获取电池图标颜色
  Color _getBatteryIconColor(BatteryInfo batteryInfo) {
    if (batteryInfo.state == BatteryState.charging) {
      return IndustrialTheme.successGreen;
    }
    
    if (batteryInfo.isLowBattery) {
      return IndustrialTheme.errorRed;
    } else if (batteryInfo.level < 40) {
      return IndustrialTheme.warningAmber;
    } else {
      return IndustrialTheme.successGreen;
    }
  }

  /// 获取电池文字颜色
  Color _getBatteryTextColor(BatteryInfo batteryInfo) {
    if (batteryInfo.isLowBattery && batteryInfo.state != BatteryState.charging) {
      return IndustrialTheme.errorRed;
    } else if (batteryInfo.level < 40 && batteryInfo.state != BatteryState.charging) {
      return IndustrialTheme.warningAmber;
    } else {
      return Colors.white;
    }
  }

  /// 获取电池背景颜色
  Color _getBatteryBackgroundColor(BatteryInfo batteryInfo) {
    if (batteryInfo.state == BatteryState.charging) {
      return IndustrialTheme.successGreen.withOpacity(0.2);
    }
    
    if (batteryInfo.isLowBattery) {
      return IndustrialTheme.errorRed.withOpacity(0.2);
    } else if (batteryInfo.level < 40) {
      return IndustrialTheme.warningAmber.withOpacity(0.2);
    } else {
      return Colors.white.withOpacity(0.2);
    }
  }

  /// 获取电池边框颜色
  Color _getBatteryBorderColor(BatteryInfo batteryInfo) {
    if (batteryInfo.state == BatteryState.charging) {
      return IndustrialTheme.successGreen;
    }
    
    if (batteryInfo.isLowBattery) {
      return IndustrialTheme.errorRed;
    } else if (batteryInfo.level < 40) {
      return IndustrialTheme.warningAmber;
    } else {
      return Colors.white.withOpacity(0.5);
    }
  }
}
