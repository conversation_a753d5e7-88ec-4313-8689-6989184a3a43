import '../service/serial_gps_service.dart';

/// GPS模块初始化测试工具
class GPSInitTest {
  static final SerialGPSService _gpsService = SerialGPSService.instance;
  
  /// 测试GPS模块初始化和低功耗指令发送
  static Future<void> testGPSInitialization() async {
    print('=== 开始测试GPS模块初始化 ===');
    
    try {
      // 尝试连接串口
      print('🔌 正在连接串口GPS模块...');
      final connected = await _gpsService.connect();
      
      if (connected) {
        print('✅ 串口GPS连接成功！');
        print('🔧 GPS模块初始化已自动执行（包含低功耗指令）');
        
        // 等待一段时间观察GPS数据
        print('📡 正在监听GPS数据（10秒）...');
        
        // 添加数据监听器
        _gpsService.addListener((gpsData) {
          print('📍 [GPS数据] 纬度: ${gpsData.latitude.toStringAsFixed(6)}, '
                '经度: ${gpsData.longitude.toStringAsFixed(6)}, '
                '高度: ${gpsData.altitude?.toStringAsFixed(1) ?? "未知"}m');
        });
        
        // 等待10秒观察数据
        await Future.delayed(Duration(seconds: 10));
        
        // 测试手动发送其他NMEA指令
        print('🧪 测试手动发送NMEA指令...');
        await _testManualNMEACommands();
        
      } else {
        print('❌ 串口GPS连接失败！');
        print('💡 请检查：');
        print('   1. GPS模块是否正确连接到 /dev/ttyS7');
        print('   2. 设备权限是否正确');
        print('   3. GPS模块是否正常工作');
      }
      
    } catch (e) {
      print('💥 测试过程中发生错误: $e');
    } finally {
      // 清理资源
      await _gpsService.disconnect();
      print('🔌 串口GPS连接已断开');
    }
    
    print('=== GPS模块初始化测试结束 ===');
  }
  
  /// 测试手动发送NMEA指令
  static Future<void> _testManualNMEACommands() async {
    print('📤 测试发送其他NMEA指令...');
    
    // 测试发送查询版本信息的指令
    await _gpsService.sendNMEACommand('\$PAIR001*3D');
    await Future.delayed(Duration(milliseconds: 500));
    
    // 测试发送查询当前配置的指令
    await _gpsService.sendNMEACommand('\$PAIR002*3E');
    await Future.delayed(Duration(milliseconds: 500));
    
    print('✅ 手动NMEA指令发送测试完成');
  }
  
  /// 验证低功耗模式是否生效
  static Future<void> verifyLowPowerMode() async {
    print('🔍 验证低功耗模式...');
    
    if (!_gpsService.isConnected) {
      print('❌ GPS模块未连接，无法验证');
      return;
    }
    
    int dataCount = 0;
    DateTime? lastUpdate;
    
    // 监听5分钟的数据更新频率
    final listener = (gpsData) {
      dataCount++;
      final now = DateTime.now();
      
      if (lastUpdate != null) {
        final interval = now.difference(lastUpdate!).inSeconds;
        print('📊 数据更新间隔: ${interval}秒');
        
        if (interval >= 8 && interval <= 12) {
          print('✅ 低功耗模式工作正常（约10秒间隔）');
        } else if (interval < 5) {
          print('⚠️ 更新频率较高，可能未进入低功耗模式');
        }
      }
      
      lastUpdate = now;
    };
    
    _gpsService.addListener(listener);
    
    print('⏱️ 监听5分钟以验证更新频率...');
    await Future.delayed(Duration(minutes: 5));
    
    print('📈 总共收到 $dataCount 次数据更新');
    print('📊 平均更新间隔: ${300 / dataCount}秒');
    
    // 移除监听器
    _gpsService.removeListener(listener);
  }
}
