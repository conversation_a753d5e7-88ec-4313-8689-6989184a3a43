import 'dart:io';

/// 测试不同的shell命令方式发送NMEA指令
void main() async {
  print('🧪 测试不同的shell命令方式');
  print('='*50);
  
  final serialPort = '/dev/ttyS7';
  
  // 测试方法1: printf命令
  await testPrintfCommand(serialPort);
  
  // 测试方法2: echo -ne命令
  await testEchoCommand(serialPort);
  
  // 测试方法3: 使用临时文件
  await testTempFileMethod(serialPort);
  
  print('='*50);
  print('✅ 所有测试完成');
}

/// 测试printf命令
Future<void> testPrintfCommand(String serialPort) async {
  print('\n📝 测试方法1: printf命令');
  
  try {
    final command = 'printf "\$PAIR650,10*14\r\n" > $serialPort';
    print('🔧 执行命令: $command');
    
    final process = await Process.start(
      'sh',
      ['-c', command],
      mode: ProcessStartMode.normal,
    );
    
    final exitCode = await process.exitCode;
    if (exitCode == 0) {
      print('✅ printf命令执行成功');
    } else {
      print('❌ printf命令执行失败，退出码: $exitCode');
      
      // 读取错误输出
      final stderr = await process.stderr.transform(utf8.decoder).join();
      if (stderr.isNotEmpty) {
        print('❌ 错误信息: $stderr');
      }
    }
    
  } catch (e) {
    print('❌ printf命令异常: $e');
  }
}

/// 测试echo命令
Future<void> testEchoCommand(String serialPort) async {
  print('\n📝 测试方法2: echo -ne命令');
  
  try {
    final command = 'echo -ne "\\\$PAIR650,10*14\\\r\\\n" > $serialPort';
    print('🔧 执行命令: $command');
    
    final process = await Process.start(
      'sh',
      ['-c', command],
      mode: ProcessStartMode.normal,
    );
    
    final exitCode = await process.exitCode;
    if (exitCode == 0) {
      print('✅ echo命令执行成功');
    } else {
      print('❌ echo命令执行失败，退出码: $exitCode');
      
      // 读取错误输出
      final stderr = await process.stderr.transform(utf8.decoder).join();
      if (stderr.isNotEmpty) {
        print('❌ 错误信息: $stderr');
      }
    }
    
  } catch (e) {
    print('❌ echo命令异常: $e');
  }
}

/// 测试临时文件方法
Future<void> testTempFileMethod(String serialPort) async {
  print('\n📝 测试方法3: 临时文件方法');
  
  try {
    // 创建临时文件
    final tempFile = File('/tmp/nmea_command.txt');
    await tempFile.writeAsString('\$PAIR650,10*14\r\n');
    
    print('📄 创建临时文件: ${tempFile.path}');
    
    // 使用cat命令将临时文件内容写入串口
    final command = 'cat ${tempFile.path} > $serialPort';
    print('🔧 执行命令: $command');
    
    final process = await Process.start(
      'sh',
      ['-c', command],
      mode: ProcessStartMode.normal,
    );
    
    final exitCode = await process.exitCode;
    if (exitCode == 0) {
      print('✅ 临时文件方法执行成功');
    } else {
      print('❌ 临时文件方法执行失败，退出码: $exitCode');
      
      // 读取错误输出
      final stderr = await process.stderr.transform(utf8.decoder).join();
      if (stderr.isNotEmpty) {
        print('❌ 错误信息: $stderr');
      }
    }
    
    // 清理临时文件
    if (await tempFile.exists()) {
      await tempFile.delete();
      print('🗑️ 临时文件已删除');
    }
    
  } catch (e) {
    print('❌ 临时文件方法异常: $e');
  }
}

/// 检查串口设备状态
Future<void> checkSerialPort(String serialPort) async {
  print('\n🔍 检查串口设备状态');
  
  try {
    final deviceFile = File(serialPort);
    final exists = await deviceFile.exists();
    print('📍 设备文件存在: $exists');
    
    if (exists) {
      final stat = await deviceFile.stat();
      print('📊 文件类型: ${stat.type}');
      print('📊 文件大小: ${stat.size}');
      print('📊 修改时间: ${stat.modified}');
    }
    
    // 检查权限
    final lsProcess = await Process.start('ls', ['-l', serialPort]);
    final lsOutput = await lsProcess.stdout.transform(utf8.decoder).join();
    print('🔐 权限信息: $lsOutput');
    
  } catch (e) {
    print('❌ 检查串口设备异常: $e');
  }
}
