import 'lib/utils/gps_init_test.dart';

/// GPS模块初始化测试主程序
void main() async {
  print('🚀 启动GPS模块初始化测试程序');
  
  try {
    print('选择测试模式：');
    print('1. 基础GPS初始化测试');
    print('2. 低功耗定时器测试（2分钟）');
    print('默认执行基础测试...\n');

    // 执行基础GPS初始化测试
    await GPSInitTest.testGPSInitialization();

    print('\n' + '='*50);
    print('基础测试完成！');
    print('如需测试定时器功能，请手动调用：');
    print('await GPSInitTest.testLowPowerTimer();');
    print('='*50);

  } catch (e) {
    print('❌ 测试程序执行失败: $e');
  }
}
